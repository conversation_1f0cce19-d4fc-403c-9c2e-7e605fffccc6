DROP TABLE restolap;

CREATE TABLE restolap AS
SELECT
  SUBSTR(ocs.mdl_get_cat_name(dep_id), 1, 30) AS department,
  dep_id AS dep_id_,
  SUBSTR(ocs.mdl_get_group_name(group_id), 1, 30) AS group_name,
  group_id AS group_id_,
  SUBSTR(ocs.mdl_get_cat_name(sg_id), 1, 30) AS subgroup,
  sg_id AS sg_id_,
  SUBSTR(ocs.mdl_get_cat_name(shortname_id), 1, 30) AS shortname,
  shortname_id AS shortname_id_,
  SUBSTR(ocs.mdl_get_pgsc(model_id), 1, 30) AS pgsc,
  model_id AS model_id_,
  SUBSTR(ocs.mdl_get_size_code(size_id), 1, 20) AS size_code,
  size_id AS size_id_,
  qty_in,
  avail,
  qty_out,
  total,
  price_7,
  level_id
FROM (
  SELECT
    dep_cl.category_id AS dep_id,
    m.group_id,
    sg_cl.category_id AS sg_id,
    sn_cl.category_id AS shortname_id,
    x.model_id,
    x.size_id,
    SUM(qty_in) AS qty_in,
    SUM(avail) AS avail,
    SUM(qty_out) AS qty_out,
    SUM(total) AS total,
    SUM(price_7) AS price_7,
    GROUPING_ID(
      dep_cl.category_id,
      m.group_id,
      sg_cl.category_id,
      sn_cl.category_id,
      x.model_id,
      x.size_id
    ) AS level_id
  FROM (
    SELECT
      -- 0 owner_id,
      -- -1 mvm_categ_id,
      model_id,
      color_id,
      size_id,
      mvm_date,
      location_id,
      SUM(ocs.prc_get_price_by_sku(model_id, color_id, size_id, 7, mvm_date, mvm_date) * total) AS price_7,
      SUM(qty_in) AS qty_in,
      (SUM(total) - SUM(qty_out)) AS avail,
      SUM(qty_out) AS qty_out,
      SUM(total) AS total
    FROM (
      SELECT /*+ ordered use_nl(locs dr) */
        model_id,
        color_id,
        size_id,
        TRUNC(SYSDATE) AS mvm_date,
        dr.location_id,
        dr.avail_qty + dr.reserv_qty AS total,
        0 AS qty_in,
        0 AS qty_out
      FROM ocs.restolap_locs locs
      INNER JOIN ocs.div_rests dr
        ON dr.location_id = locs.location_id

      UNION ALL

      SELECT /*+ leading(m) use_nl(m locs ms) index(m IDX$MOVEMENT_DATCOR) */
        ms.model_id,
        ms.color_id,
        ms.size_id,
        TRUNC(SYSDATE),
        m.location_to_id,
        0,
        ms.qty,
        0
      FROM ocs.restolap_locs locs
      INNER JOIN ocs.movements m
        ON m.location_to_id = locs.location_id
      INNER JOIN ocs.mvm_sku ms
        ON m.movement_id = ms.movement_id
      INNER JOIN ocs.mvm_types mt
        ON m.mvm_type_id = mt.mvm_type_id
      WHERE m.date_correction IS NULL
        AND mt.corr_dest = 1

      UNION ALL

      SELECT /*+ leading(m) use_nl(m locs ms) index(m IDX$MOVEMENT_DATCOR) */
        ms.model_id,
        ms.color_id,
        ms.size_id,
        TRUNC(SYSDATE),
        m.location_from_id,
        0,
        0,
        ms.qty
      FROM ocs.restolap_locs locs
      INNER JOIN ocs.movements m
        ON m.location_from_id = locs.location_id
      INNER JOIN ocs.mvm_sku ms
        ON m.movement_id = ms.movement_id
      INNER JOIN ocs.mvm_types mt
        ON m.mvm_type_id = mt.mvm_type_id
      WHERE m.date_correction IS NULL
        AND mt.corr_src = 1
    )
    GROUP BY
      model_id,
      color_id,
      size_id,
      mvm_date,
      location_id
    HAVING
      SUM(total) != 0
      OR SUM(qty_in) != 0
      OR SUM(qty_out) != 0
  ) x
  LEFT JOIN ocs.category_lnk brand_cl
    ON x.model_id = brand_cl.model_id
    AND brand_cl.category_type_id = 20
  LEFT JOIN ocs.category_lnk dep_cl
    ON x.model_id = dep_cl.model_id
    AND dep_cl.category_type_id = 3
  INNER JOIN ocs.model m
    ON x.model_id = m.model_id
  LEFT JOIN ocs.category_lnk sg_cl
    ON x.model_id = sg_cl.model_id
    AND sg_cl.category_type_id = 5
  LEFT JOIN ocs.category_lnk sn_cl
    ON x.model_id = sn_cl.model_id
    AND sn_cl.category_type_id = ocs.c_cat_type_короткое_наименов
  WHERE
    NVL(brand_cl.category_id, -1) IN (1168)
  GROUP BY GROUPING SETS (
    (
      dep_cl.category_id,
      m.group_id,
      sg_cl.category_id,
      sn_cl.category_id,
      x.model_id,
      x.size_id
    ),
    ()
  )
);

SELECT *
FROM restolap
WHERE 1=1
ORDER BY
  department,
  group_name,
  subgroup,
  shortname,
  pgsc,
  size_code,
  level_id
;