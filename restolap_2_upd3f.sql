WITH
-- 1. All locations eligible for this report (filtered by division group)
eligible_locations AS (
  SELECT
    pa.location_id
  FROM
    ocs.phys_alloc pa
    INNER JOIN ocs.div_groups dg
      ON pa.log_division_id = dg.division_id
  WHERE
    dg.div_grp_type_id = 1
    AND dg.div_grp_id IN (151)
    -- Optional alternative group set:
    -- AND dg.div_grp_id IN (304, 314, 151, 102, 306, 520)
),

-- 2. SKU-level movement data for all eligible locations:
--    - current stock (div_rests)
--    - inbound movements
--    - outbound movements
location_sku_movements AS (
  -- Current stock per location/SKU
  SELECT /*+ ordered use_nl(locs dr) */
    dr.model_id,
    dr.color_id,
    dr.size_id,
    TRUNC(SYSDATE) AS mvm_date,
    dr.location_id,
    0 AS qty_in,
    0 AS qty_out,
    dr.avail_qty + dr.reserv_qty AS total
  FROM
    eligible_locations locs
    INNER JOIN ocs.div_rests dr
      ON dr.location_id = locs.location_id

  UNION ALL

  -- Inbound movements to eligible locations
  SELECT /*+ leading(m) use_nl(m locs ms) index(m IDX$MOVEMENT_DATCOR) */
    ms.model_id,
    ms.color_id,
    ms.size_id,
    TRUNC(SYSDATE),
    m.location_to_id,
    ms.qty AS qty_in,
    0 AS qty_out,
    0 AS total
  FROM
    eligible_locations locs
    INNER JOIN ocs.movements m
      ON m.location_to_id = locs.location_id
    INNER JOIN ocs.mvm_sku ms
      ON m.movement_id = ms.movement_id
    INNER JOIN ocs.mvm_types mt
      ON m.mvm_type_id = mt.mvm_type_id
  WHERE
    m.date_correction IS NULL
    AND mt.corr_dest = 1

  UNION ALL

  -- Outbound movements from eligible locations
  SELECT /*+ leading(m) use_nl(m locs ms) index(m IDX$MOVEMENT_DATCOR) */
    ms.model_id,
    ms.color_id,
    ms.size_id,
    TRUNC(SYSDATE),
    m.location_from_id,
    0 AS qty_in,
    ms.qty AS qty_out,
    0 AS total
  FROM
    eligible_locations locs
    INNER JOIN ocs.movements m
      ON m.location_from_id = locs.location_id
    INNER JOIN ocs.mvm_sku ms
      ON m.movement_id = ms.movement_id
    INNER JOIN ocs.mvm_types mt
      ON m.mvm_type_id = mt.mvm_type_id
  WHERE
    m.date_correction IS NULL
    AND mt.corr_src = 1
),

-- 3. Aggregated inventory data with price and grouping level info
aggregated_inventory AS (
  SELECT
    dep_cl.category_id AS dep_id,
    m.group_id,
    sg_cl.category_id AS sg_id,
    sn_cl.category_id AS shortname_id,
    lsm.model_id,
    lsm.size_id,
    lsm.color_id,
    SUM(lsm.qty_in) AS qty_in,
    SUM(lsm.total - lsm.qty_out) AS avail,
    SUM(lsm.qty_out) AS qty_out,
    SUM(lsm.total) AS total,
    SUM(ocs.prc_get_price_by_sku(lsm.model_id, lsm.color_id, lsm.size_id, 7, mvm_date, mvm_date) * lsm.total) AS price_7,
    GROUPING_ID(
      dep_cl.category_id,
      m.group_id,
      sg_cl.category_id,
      sn_cl.category_id,
      lsm.model_id,
      lsm.size_id,
      lsm.color_id
    ) AS level_id
  FROM
    location_sku_movements lsm
    LEFT JOIN ocs.category_lnk brand_cl
      ON lsm.model_id = brand_cl.model_id
      AND brand_cl.category_type_id = 20
    LEFT JOIN ocs.category_lnk dep_cl
      ON lsm.model_id = dep_cl.model_id
      AND dep_cl.category_type_id = 3
    INNER JOIN ocs.model m
      ON lsm.model_id = m.model_id
    LEFT JOIN ocs.category_lnk sg_cl
      ON lsm.model_id = sg_cl.model_id
      AND sg_cl.category_type_id = 5
    LEFT JOIN ocs.category_lnk sn_cl
      ON lsm.model_id = sn_cl.model_id
      AND sn_cl.category_type_id = ocs.c_cat_type_короткое_наименов
  WHERE
    NVL(brand_cl.category_id, -1) = :p_brand_id
  GROUP BY GROUPING SETS (
    (
      dep_cl.category_id,
      m.group_id,
      sg_cl.category_id,
      sn_cl.category_id,
      lsm.model_id,
      lsm.size_id,
      lsm.color_id
    ),
    () -- grand total
  )
  HAVING
    SUM(lsm.total) != 0
    OR SUM(lsm.qty_in) != 0
    OR SUM(lsm.qty_out) != 0
),

-- 4. Add qty_ecom / qty_retail channel-specific quantities once per SKU
inventory_with_channel_qty AS (
  SELECT
    ai.*,
    i.item_id,
    ocs.pck_lot.get_thing_qty_th_bron_wms(i.item_id, 'MAX_MONTHES_EXP_TO_ECOM', :p_wms_zones) AS qty_ecom,
    ocs.pck_lot.get_thing_qty_th_bron_wms(i.item_id, 'MAX_MONTHES_EXP_TO_RETAIL', :p_wms_zones) AS qty_retail
  FROM
    aggregated_inventory ai
    LEFT JOIN ocs.item i
      ON ai.model_id = i.model_id
      AND ai.size_id = i.size_id
      AND ai.color_id = i.color_id
)

-- 5. Final select with calculated names and sorted by available minus retail
SELECT
  SUBSTR(ocs.mdl_get_cat_name(dep_id), 1, 30) AS department,
  dep_id AS dep_id_,
  SUBSTR(ocs.mdl_get_group_name(group_id), 1, 30) AS group_name,
  group_id AS group_id_,
  SUBSTR(ocs.mdl_get_cat_name(sg_id), 1, 30) AS subgroup,
  sg_id AS sg_id_,
  SUBSTR(ocs.mdl_get_cat_name(shortname_id), 1, 30) AS shortname,
  shortname_id AS shortname_id_,
  SUBSTR(ocs.mdl_get_pgsc(model_id), 1, 30) AS pgsc,
  model_id AS model_id_,
  SUBSTR(ocs.mdl_get_size_code(size_id), 1, 20) AS size_code,
  size_id AS size_id_,
  item_id,
  qty_in,
  qty_out,
  total,
  avail,
  qty_ecom,
  qty_retail,
  price_7,
  level_id
FROM
  inventory_with_channel_qty
ORDER BY
  avail - qty_retail DESC,
  department,
  group_name,
  subgroup,
  shortname,
  pgsc,
  size_code,
  level_id
;
